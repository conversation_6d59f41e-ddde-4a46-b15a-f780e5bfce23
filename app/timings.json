{"durations": [{"spec": "tests/cypress/integration/addCoach.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/addStudent.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/changeActiveSite.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/changeSchoolYear.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/classwideIntervention.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/classwideInterventionPrinting.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/customDate.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/dataAdmin.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/externalAssessmentScoresUpload.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/fileUploadAddStudents.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/fileUploadFullRoster.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/forceClasswideIntervention.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/individualIntervention.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/instructionalVideos.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/manageAccounts.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/manageClass.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/manageUsers.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/moveStudent.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/printingAssessments.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/progressMonitoring.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/rosteringOption.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/schoolOverview.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/schoolOverviewPrint.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/screening.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/screeningAssessments.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/screeningPrinting.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/secondaryTeachers.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/setupAccount.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/studentPrinting.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/studentsSorting.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/supportUser.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/switchRoles.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/universalCoach.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/universalDataAdmin.spec.js", "duration": 10000}, {"spec": "tests/cypress/integration/usersSearch.spec.js", "duration": 10000}, {"spec": "cypress/e2e/spec-a.cy.js", "duration": 10071}, {"spec": "cypress/e2e/chunks.cy.js", "duration": 230}, {"spec": "cypress/e2e/timings.cy.js", "duration": 102}]}